#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏AppID查找器
扫描指定目录下的游戏文件夹，查找对应的Steam AppID
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Tuple, Set


class GameAppIDFinder:
    def __init__(self, game_list_file: str = "单机游戏列表.txt"):
        """
        初始化游戏AppID查找器
        
        Args:
            game_list_file: 包含AppID和游戏名称的文件路径
        """
        self.game_list_file = game_list_file
        self.appid_dict = {}
        self.load_game_list()
    
    def load_game_list(self):
        """从文件加载游戏列表和AppID映射"""
        try:
            with open(self.game_list_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and '----' in line:
                        parts = line.split('----', 1)
                        if len(parts) == 2:
                            appid = parts[0].strip()
                            game_name = parts[1].strip()
                            # 存储多种格式的游戏名称作为键
                            self.appid_dict[game_name] = appid
                            # 去除特殊字符的版本
                            clean_name = self.clean_game_name(game_name)
                            if clean_name != game_name:
                                self.appid_dict[clean_name] = appid
            
            print(f"成功加载 {len(self.appid_dict)} 个游戏记录")
        
        except FileNotFoundError:
            print(f"错误：找不到游戏列表文件 {self.game_list_file}")
        except Exception as e:
            print(f"加载游戏列表时出错：{e}")
    
    def clean_game_name(self, name: str) -> str:
        """
        清理游戏名称，去除特殊字符和标点符号
        
        Args:
            name: 原始游戏名称
            
        Returns:
            清理后的游戏名称
        """
        # 去除常见的特殊字符和标点
        cleaned = re.sub(r'[™®©：:：\-\s\(\)（）\[\]【】《》""''""\'\'\.。,，!！?？]', '', name)
        # 去除版本号相关词汇
        cleaned = re.sub(r'(重制版|豪华版|完整版|年度版|特别版|终极版|加强版|DX|HD|2D|3D|VR)', '', cleaned)
        return cleaned.lower()
    
    def fuzzy_match_game_name(self, folder_name: str) -> Tuple[str, str]:
        """
        模糊匹配游戏名称
        
        Args:
            folder_name: 文件夹名称
            
        Returns:
            匹配到的游戏名称和AppID，如果没找到返回空字符串
        """
        folder_clean = self.clean_game_name(folder_name)
        
        # 1. 精确匹配
        if folder_name in self.appid_dict:
            return folder_name, self.appid_dict[folder_name]
        
        # 2. 清理后的名称匹配
        for game_name, appid in self.appid_dict.items():
            if self.clean_game_name(game_name) == folder_clean:
                return game_name, appid
        
        # 3. 包含匹配（文件夹名包含游戏名）
        for game_name, appid in self.appid_dict.items():
            game_clean = self.clean_game_name(game_name)
            if game_clean and game_clean in folder_clean:
                return game_name, appid
        
        # 4. 反向包含匹配（游戏名包含文件夹名）
        for game_name, appid in self.appid_dict.items():
            game_clean = self.clean_game_name(game_name)
            if folder_clean and folder_clean in game_clean:
                return game_name, appid
        
        return "", ""
    
    def scan_game_directory(self, game_dir: str) -> Tuple[List[Tuple[str, str, str]], List[str]]:
        """
        扫描游戏目录，查找AppID
        
        Args:
            game_dir: 游戏目录路径
            
        Returns:
            找到的游戏列表(文件夹名, 匹配的游戏名, AppID)和未找到的文件夹列表
        """
        found_games = []
        not_found = []
        
        if not os.path.exists(game_dir):
            print(f"错误：目录不存在 {game_dir}")
            return found_games, not_found
        
        print(f"正在扫描目录：{game_dir}")
        
        try:
            # 获取所有子文件夹
            folders = [f for f in os.listdir(game_dir) 
                      if os.path.isdir(os.path.join(game_dir, f))]
            
            print(f"找到 {len(folders)} 个子文件夹")
            
            for folder in sorted(folders):
                print(f"正在处理：{folder}")
                
                matched_name, appid = self.fuzzy_match_game_name(folder)
                
                if appid:
                    found_games.append((folder, matched_name, appid))
                    print(f"  ✓ 找到匹配：{matched_name} -> {appid}")
                else:
                    not_found.append(folder)
                    print(f"  ✗ 未找到匹配")
        
        except Exception as e:
            print(f"扫描目录时出错：{e}")
        
        return found_games, not_found
    
    def save_results(self, found_games: List[Tuple[str, str, str]], 
                    not_found: List[str], output_file: str = "找到的AppID.txt"):
        """
        保存查找结果到文件
        
        Args:
            found_games: 找到的游戏列表
            not_found: 未找到的游戏列表
            output_file: 输出文件名
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=== 找到AppID的游戏 ===\n\n")
                for folder, matched_name, appid in found_games:
                    f.write(f"{appid}----{matched_name}----{folder}\n")
                
                f.write(f"\n=== 统计信息 ===\n")
                f.write(f"找到AppID的游戏数量：{len(found_games)}\n")
                f.write(f"未找到AppID的游戏数量：{len(not_found)}\n")
                
                if not_found:
                    f.write(f"\n=== 未找到AppID的游戏 ===\n\n")
                    for folder in not_found:
                        f.write(f"{folder}\n")
            
            print(f"\n结果已保存到：{output_file}")
        
        except Exception as e:
            print(f"保存结果时出错：{e}")
    
    def print_summary(self, found_games: List[Tuple[str, str, str]], not_found: List[str]):
        """打印查找结果摘要"""
        print(f"\n{'='*50}")
        print(f"查找结果摘要")
        print(f"{'='*50}")
        print(f"找到AppID的游戏：{len(found_games)} 个")
        print(f"未找到AppID的游戏：{len(not_found)} 个")
        
        if not_found:
            print(f"\n未找到AppID的游戏列表：")
            for i, folder in enumerate(not_found, 1):
                print(f"{i:3d}. {folder}")


def main():
    """主函数"""
    # 游戏目录路径
    game_directory = r"d:\Downloads\单机游戏\单机游戏"
    
    # 创建查找器实例
    finder = GameAppIDFinder()
    
    # 扫描游戏目录
    found_games, not_found = finder.scan_game_directory(game_directory)
    
    # 保存结果
    finder.save_results(found_games, not_found)
    
    # 打印摘要
    finder.print_summary(found_games, not_found)


if __name__ == "__main__":
    main()
